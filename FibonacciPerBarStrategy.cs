//
// Fibonacci Per Bar Strategy - TradingView Style Display
// Converted from Pine Script with proper Fibonacci visualization
//
#region Using declarations
using System;
using System.ComponentModel.DataAnnotations;
using System.Windows.Media;
using NinjaTrader.Cbi;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

namespace NinjaTrader.NinjaScript.Strategies
{
    public class FibonacciPerBarStrategy : Strategy
    {
        #region Variables
        // Settings
        private bool useMtfMode = false;
        private int mtfPeriodValue = 5;
        private bool showSignals = true;
        private bool showFibLevels = true;
        private bool showFibLabels = true;
        private int maxBarsBack = 50;
        private bool signalsOnly = false;

        // Fibonacci levels and colors (matching TradingView style)
        private readonly double[] fibLevels = { 1.1, 1.08, 1.0, 0.9, 0.1, 0.0, -0.08, -0.1 };
        private readonly Brush[] fibColors = {
            Brushes.Cyan,      // 110%
            Brushes.Blue,      // 108%
            Brushes.Green,     // 100% (green line)
            Brushes.Orange,    // 90%
            Brushes.Purple,    // 10%
            Brushes.Red,       // 0% (red line)
            Brushes.Magenta,   // -8%
            Brushes.Yellow     // -10%
        };

        // Current Fibonacci levels for trading
        private double greenLevel = double.NaN;  // 1.0 level (100%)
        private double redLevel = double.NaN;    // 0.0 level (0%)
        private double tpBuyLevel = double.NaN;  // 1.1 level (110%)
        private double tpSellLevel = double.NaN; // -0.1 level (-10%)

        // Position tracking
        private bool inLongPosition = false;
        private bool inShortPosition = false;

        // MTF tracking
        private int lastMtfBar = -1;
        private DateTime lastMtfTime = DateTime.MinValue;
        #endregion

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = @"Simple Fibonacci Per Bar Strategy";
                Name = "FibonacciPerBarStrategy";
                Calculate = Calculate.OnBarClose;
                EntriesPerDirection = 1;
                EntryHandling = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = true;
                ExitOnSessionCloseSeconds = 30;
                IsFillLimitOnTouch = false;
                MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution = OrderFillResolution.Standard;
                Slippage = 0;
                StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Gtc;
                TraceOrders = false;
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade = 20;
                IsInstantiatedOnEachOptimizationIteration = false;
            }
            else if (State == State.Configure)
            {
                // Add MTF data series if enabled
                if (useMtfMode)
                {
                    AddDataSeries(BarsPeriodType.Minute, mtfPeriodValue);
                }
            }
        }

        protected override void OnBarUpdate()
        {
            if (CurrentBar < BarsRequiredToTrade)
                return;

            // Only process on primary data series
            if (BarsInProgress != 0)
                return;

            // Clean up old drawing objects to prevent chart clutter
            CleanupOldDrawObjects();

            // Calculate Fibonacci levels based on current bar or MTF
            CalculateFibLevels();

            // Check for trading signals
            CheckForSignals();
        }

        private void CleanupOldDrawObjects()
        {
            // Remove drawing objects older than maxBarsBack to match TradingView behavior
            if (CurrentBar > maxBarsBack)
            {
                int oldBar = CurrentBar - maxBarsBack;

                // Remove old Fibonacci levels
                string oldFibTag = useMtfMode ? "MTFFib" + oldBar : "Fib" + oldBar;

                for (int i = 0; i < fibLevels.Length; i++)
                {
                    double fibLevel = fibLevels[i];
                    string oldLineTag = oldFibTag + "_Level_" + fibLevel.ToString("F2");
                    string oldLabelTag = oldFibTag + "_Label_" + fibLevel.ToString("F2");

                    RemoveDrawObject(oldLineTag);
                    RemoveDrawObject(oldLabelTag);
                }
            }
        }

        private void CalculateFibLevels()
        {
            double high, low;
            bool shouldDraw = false;
            int startBar = 0;
            int endBar = 0;

            if (useMtfMode && BarsArray.Length > 1)
            {
                // Use MTF data
                var mtfBars = BarsArray[1];
                if (mtfBars.Count < 1) return;

                // Check if we have a new MTF period
                DateTime currentMtfTime = mtfBars.GetTime(mtfBars.Count - 1);
                if (currentMtfTime != lastMtfTime)
                {
                    shouldDraw = true;
                    lastMtfTime = currentMtfTime;

                    // Calculate how many bars this MTF period spans
                    TimeSpan mtfPeriod = TimeSpan.FromMinutes(mtfPeriodValue);
                    TimeSpan currentPeriod = BarsPeriod.Value2 == 1 ? TimeSpan.FromMinutes(1) : TimeSpan.FromSeconds(BarsPeriod.Value2);
                    int barsInMtfPeriod = (int)(mtfPeriod.TotalSeconds / currentPeriod.TotalSeconds);

                    startBar = Math.Min(barsInMtfPeriod - 1, CurrentBar);
                    endBar = 0;
                }

                high = mtfBars.GetHigh(mtfBars.Count - 1);
                low = mtfBars.GetLow(mtfBars.Count - 1);
            }
            else
            {
                // Use current bar data - ALWAYS draw for every bar when not in MTF mode
                shouldDraw = true;
                startBar = 0;
                endBar = 1;  // Draw line extending to next bar
                high = High[0];
                low = Low[0];
            }

            if (!shouldDraw) return;

            double range = high - low;
            if (range > 0)
            {
                // Update trading levels
                greenLevel = low + (range * 1.0);    // 100% level (green line)
                redLevel = low + (range * 0.0);      // 0% level (red line)
                tpBuyLevel = low + (range * 1.1);    // 110% level (buy TP)
                tpSellLevel = low + (range * -0.1);  // -10% level (sell TP)

                // Draw TradingView-style Fibonacci levels
                if (showFibLevels)
                {
                    DrawFibonacciLevels(low, range, startBar, endBar);
                }
            }
        }

        private void DrawFibonacciLevels(double rangeLow, double priceRange, int startBar, int endBar)
        {
            string baseTag = useMtfMode ? "MTFFib" + CurrentBar : "Fib" + CurrentBar;

            // Draw each Fibonacci level
            for (int i = 0; i < fibLevels.Length; i++)
            {
                double fibLevel = fibLevels[i];
                double fibPrice = rangeLow + (priceRange * fibLevel);
                Brush fibColor = fibColors[i];

                // Create horizontal line spanning the timeframe period
                string lineTag = baseTag + "_Level_" + fibLevel.ToString("F2");

                // For single bar mode, draw line extending slightly beyond the bar
                if (!useMtfMode)
                {
                    Draw.Line(this, lineTag, startBar, fibPrice, endBar + 1, fibPrice, fibColor);
                }
                else
                {
                    // For MTF mode, draw line spanning the entire MTF period
                    Draw.Line(this, lineTag, startBar, fibPrice, endBar, fibPrice, fibColor);
                }

                // Add label with percentage and price if enabled
                if (showFibLabels)
                {
                    string percentText = (fibLevel * 100).ToString("F0") + "%";
                    string priceText = fibPrice.ToString("F2");
                    string labelText = percentText + " (" + priceText + ")";
                    string labelTag = baseTag + "_Label_" + fibLevel.ToString("F2");

                    // Position label at the right side of the line for better visibility
                    int labelBar = useMtfMode ? endBar : endBar + 1;
                    Draw.Text(this, labelTag, labelText, labelBar, fibPrice, fibColor);
                }
            }
        }

        private void CheckForSignals()
        {
            if (!showSignals || double.IsNaN(greenLevel) || double.IsNaN(redLevel))
                return;

            // Check for buy signal (price hits green level)
            if (High[0] >= greenLevel && Low[0] <= greenLevel && (signalsOnly || Position.MarketPosition == MarketPosition.Flat))
            {
                // Only execute orders if not in signals-only mode
                if (!signalsOnly)
                {
                    EnterLong(1, "FibBuy");
                    inLongPosition = true;

                    // Set TP and SL
                    SetProfitTarget("FibBuy", CalculationMode.Price, tpBuyLevel);
                    SetStopLoss("FibBuy", CalculationMode.Price, redLevel, false);
                }

                Draw.ArrowUp(this, "BuySignal" + CurrentBar, false, 0, Low[0] - TickSize, Brushes.Green);
                Print("BUY signal at " + greenLevel.ToString("F2") + (signalsOnly ? " (SIGNAL ONLY)" : ""));
            }

            // Check for sell signal (price hits red level)
            if (Low[0] <= redLevel && High[0] >= redLevel && (signalsOnly || Position.MarketPosition == MarketPosition.Flat))
            {
                // Only execute orders if not in signals-only mode
                if (!signalsOnly)
                {
                    EnterShort(1, "FibSell");
                    inShortPosition = true;

                    // Set TP and SL
                    SetProfitTarget("FibSell", CalculationMode.Price, tpSellLevel);
                    SetStopLoss("FibSell", CalculationMode.Price, greenLevel, false);
                }

                Draw.ArrowDown(this, "SellSignal" + CurrentBar, false, 0, High[0] + TickSize, Brushes.Red);
                Print("SELL signal at " + redLevel.ToString("F2") + (signalsOnly ? " (SIGNAL ONLY)" : ""));
            }
        }

        protected override void OnPositionUpdate(Position position, double averagePrice, int quantity, MarketPosition marketPosition)
        {
            if (marketPosition == MarketPosition.Flat)
            {
                inLongPosition = false;
                inShortPosition = false;
            }
        }

        #region Properties
        [NinjaScriptProperty]
        [Display(Name = "Use Multi-Timeframe Mode", Description = "Enable MTF mode", Order = 1, GroupName = "Settings")]
        public bool UseMtfMode
        {
            get { return useMtfMode; }
            set { useMtfMode = value; }
        }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "MTF Period Value", Description = "MTF Period in minutes", Order = 2, GroupName = "Settings")]
        public int MtfPeriodValue
        {
            get { return mtfPeriodValue; }
            set { mtfPeriodValue = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "Show Signals", Description = "Show buy/sell signals", Order = 3, GroupName = "Settings")]
        public bool ShowSignals
        {
            get { return showSignals; }
            set { showSignals = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "Show Fibonacci Levels", Description = "Display Fibonacci retracement levels", Order = 4, GroupName = "Display")]
        public bool ShowFibLevels
        {
            get { return showFibLevels; }
            set { showFibLevels = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "Show Fibonacci Labels", Description = "Display labels with percentages and prices", Order = 5, GroupName = "Display")]
        public bool ShowFibLabels
        {
            get { return showFibLabels; }
            set { showFibLabels = value; }
        }

        [NinjaScriptProperty]
        [Range(1, 200)]
        [Display(Name = "Maximum Bars Back", Description = "Maximum number of bars to display levels", Order = 6, GroupName = "Display")]
        public int MaxBarsBack
        {
            get { return maxBarsBack; }
            set { maxBarsBack = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "Signals Only (No Trading)", Description = "Show signals without executing any trades", Order = 7, GroupName = "Settings")]
        public bool SignalsOnly
        {
            get { return signalsOnly; }
            set { signalsOnly = value; }
        }
        #endregion

    }
}
